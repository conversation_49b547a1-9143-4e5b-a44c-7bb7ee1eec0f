import React from 'react';
import classNames from 'classnames';
import {Card, Row, Col} from 'react-bootstrap';
import '../styles/styles.scss';
import { CheckedCircleIcon } from './svgIcons';
// import {RoundCheckFilled} from '../utils/svgIcons';


type StepperProps = {
  steps: string[];
  currentStep: number;
  setStep: (step: number) => void;
};

const Stepper: React.FC<StepperProps> = ({steps, currentStep, setStep}) => {
  return (
    <div className="stepper-card-container">
      {steps.map((label, index) => {
        const stepNumber = index + 1;
        const isCompleted = stepNumber < currentStep;
        const isActive = stepNumber === currentStep;

        return (
          <Card
            key={label}
            onClick={() => {
              if (isCompleted) setStep(stepNumber);
            }}
            className={classNames("stepper-card", {
              "stepper-card-active": isActive,
              "stepper-card-completed": isCompleted,
            })}
          >
            <Row className="stepper-card-row">
              <Col>
                <div className="stepper-card-step">
                  Step {stepNumber}
                </div>
                <div className="stepper-card-lbl">
                  {label}
                </div>
              </Col>
              <Col xs="auto">
                {isCompleted ? (
                  <CheckedCircleIcon />
                ) : (
                  <div className="stepper-card-unchecked-crl" />
                )}
              </Col>
            </Row>
          </Card>
        );
      })}
    </div>
  );
};

export default Stepper;
