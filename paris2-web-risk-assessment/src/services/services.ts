import httpService from './http-service';
import {
  MostlyUsedTemplateResponse,
  Template,
  TemplateListResponse,
  TemplateUserResponse,
} from '../types/template';
import {RAItemFull, RiskListResponse} from '../types/risk';
import {
  CommonStringOptionResponse,
  CrewMember,
  OfficeApprover,
  IdName,
  OfficeItem,
  VesselData,
} from '../types';
import {ApprovalStatus} from '../enums';

/**
 * Retrieves the list of risk categories available in the system.
 * Used for categorizing risks during risk assessment creation.
 *
 * @returns {Promise<any>} Promise that resolves to the list of risk categories
 * @throws {Error} When the API request fails
 */
export const getRiskCategoryList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/categories`);

  return data;
};

/**
 * Retrieves the list of hazards available in the system.
 * Hazards are potential sources of harm that can be identified during risk assessment.
 *
 * @returns {Promise<any>} Promise that resolves to the list of hazards
 * @throws {Error} When the API request fails
 */
export const getHazardsList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/hazards`);

  return data;
};

/**
 * Retrieves the list of risk parameter types used in risk assessment calculations.
 * These parameters define various aspects of risk evaluation.
 *
 * @returns {Promise<any>} Promise that resolves to the list of risk parameter types
 * @throws {Error} When the API request fails
 */
export const getRiskParameterType = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/parameters`);
  return data;
};

/**
 * Retrieves the main risk parameter types, optionally filtered by those required for risk rating.
 * These parameters are used in the core risk assessment calculation logic.
 *
 * @param {boolean} [isRequiredForRiskRating] - Optional flag to filter parameters required for risk rating
 * @returns {Promise<any>} Promise that resolves to the list of main risk parameter types
 * @throws {Error} When the API request fails
 */
export const getMainRiskParameterType = async (
  isRequiredForRiskRating?: boolean,
) => {
  const params = isRequiredForRiskRating
    ? {is_required_for_risk_rating: true}
    : undefined;
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/parameter-types`, {
      params,
    });

  return data;
};

/**
 * Retrieves the list of task reliability assessments.
 * Used to evaluate the reliability of specific tasks within risk assessments.
 *
 * @returns {Promise<any>} Promise that resolves to the list of task reliability assessments
 * @throws {Error} When the API request fails
 */
export const getTaskReliabilityAssessList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/task-reliability-assessments`);

  return data;
};

/**
 * Generates URL query parameters string from a key and array of values.
 * Utility function to create properly encoded query strings for API requests.
 *
 * @param {string} key - The parameter key name
 * @param {string[]} valuesArray - Array of values to be encoded as query parameters
 * @returns {string} Formatted query parameter string
 * @example
 * generateQueryParams('group', ['TechD', 'Admin'])
 * // Returns: "group=TechD&group=Admin"
 */
export const generateQueryParams = (key: string, valuesArray: string[]) => {
  return valuesArray
    .map(value => `${key}=${encodeURIComponent(value)}`)
    .join(`&`);
};

/**
 * Generates the endpoint URL for retrieving vessel user details from Keycloak.
 * Constructs a query string for fetching users based on group membership.
 *
 * @param {string} [key='group'] - The attribute key to filter by (default: 'group')
 * @param {string[]} [ids=['TechD']] - Array of group IDs to filter by (default: ['TechD'])
 * @returns {string} Complete endpoint URL with query parameters
 * @example
 * GET_VESSEL_USER_DETAILS('group', ['TechD', 'Admin'])
 * // Returns: "/keycloak-admin/users?returnAttribute=ship_party_id&group=TechD&group=Admin"
 */
export const GET_VESSEL_USER_DETAILS = (key = 'group', ids = ['TechD']) => {
  const queryString = `returnAttribute=ship_party_id&${generateQueryParams(
    key,
    ids,
  )}`;

  return `/keycloak-admin/users?${queryString}`;
};

// Endpoint identifier for template listing
const TEMPLATE_LIST_ENDPOINT = 'template-list';

/**
 * Retrieves a paginated list of risk assessment templates.
 * Supports filtering, sorting, and pagination. Includes request cancellation to prevent
 * multiple concurrent requests.
 *
 * @param {Object} params - Query parameters object
 * @param {number} [params.page] - Page number for pagination
 * @param {number} [params.limit] - Number of items per page
 * @param {Record<string, string | number | string[]>} params - Additional filter parameters
 * @returns {Promise<Template[]>} Promise that resolves to the list of templates
 * @throws {Error} When the API request fails
 */
export const getTemplateList = async (
  params: {
    page?: number;
    limit?: number;
  } & Record<string, string | number | string[]>,
) => {
  const signal = httpService.cancelPreviousRequest(TEMPLATE_LIST_ENDPOINT);
  const {data} = await httpService
    .getAxiosClient()
    .get<TemplateListResponse>(
      `${process.env.API_RISK_ASSESSMENT_URL}/templates`,
      {
        params,
        signal, // Use the abort signal
      },
    );
  return data.result;
};

/**
 * Retrieves the list of users who have created or modified templates.
 * Used for filtering templates by user in the template management interface.
 *
 * @returns {Promise<any>} Promise that resolves to the list of template users
 * @throws {Error} When the API request fails
 */
export const getTemplateUserList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<TemplateUserResponse>(
      `${process.env.API_RISK_ASSESSMENT_URL}/templates/users`,
    );

  return data.result;
};

/**
 * Marks a specific template as archived (inactive).
 * Archived templates are not available for creating new risk assessments but remain
 * in the system for historical reference.
 *
 * @param {number} templateId - The unique identifier of the template to archive
 * @returns {Promise<any>} Promise that resolves to the operation result
 * @throws {Error} When the API request fails or template is not found
 */
export const markTemplateAsArchived = async (templateId: number) => {
  const {data} = await httpService
    .getAxiosClient()
    .patch(
      `${process.env.API_RISK_ASSESSMENT_URL}/templates/${templateId}/inactive`,
    );
  return data;
};

/**
 * Retrieves the most frequently used templates for quick access.
 * Returns the top 4 templates based on usage statistics to improve user experience.
 *
 * @returns {Promise<any>} Promise that resolves to the list of most used templates (max 4)
 * @throws {Error} When the API request fails
 */
export const getMostlyUsedTemplates = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<MostlyUsedTemplateResponse>(
      `${process.env.API_RISK_ASSESSMENT_URL}/templates/top?maxCount=4`,
    );
  return data.result;
};

// Endpoint identifier for risk assessment listing
const RA_LIST_ENDPOINT = 'ra-list';

/**
 * Retrieves a paginated list of risk assessments.
 * Supports filtering, sorting, and pagination. Includes request cancellation to prevent
 * multiple concurrent requests.
 *
 * @param {Object} params - Query parameters object
 * @param {number} [params.page] - Page number for pagination
 * @param {number} [params.limit] - Number of items per page
 * @param {Record<string, string | number | string[]>} params - Additional filter parameters
 * @returns {Promise<any>} Promise that resolves to the list of risk assessments
 * @throws {Error} When the API request fails
 */
export const getRAList = async (
  params: {
    page?: number;
    limit?: number;
  } & Record<string, string | number | string[]>,
) => {
  const signal = httpService.cancelPreviousRequest(RA_LIST_ENDPOINT);
  const {data} = await httpService
    .getAxiosClient()
    .get<RiskListResponse>(`${process.env.API_RISK_ASSESSMENT_URL}/risks`, {
      params,
      signal, // Use the abort signal
    });
  return data.result;
};

/**
 * Retrieves string-based filter options for risk assessments.
 * Used to populate dropdown filters in the risk assessment interface.
 *
 * @param {'vessel_category' | 'vessel_tech_group'} optionType - Type of options to retrieve
 * @returns {Promise<any>} Promise that resolves to the list of string options
 * @throws {Error} When the API request fails or invalid option type is provided
 */
export const getRAStringOptions = async (
  optionType: 'vessel_category' | 'vessel_tech_group',
) => {
  const {data} = await httpService
    .getAxiosClient()
    .get<CommonStringOptionResponse>(
      `${process.env.API_RISK_ASSESSMENT_URL}/risks/options/${optionType}`,
    );

  return data;
};

/**
 * Retrieves a specific template by its unique identifier.
 * Used for editing existing templates or creating new risk assessments from templates.
 *
 * @param {string} id - The unique identifier of the template
 * @returns {Promise<{message: string; result: Template}>} Promise that resolves to the template data
 * @throws {Error} When the API request fails or template is not found
 */
export const getTemplateById = async (id: string) => {
  const {data} = await httpService
    .getAxiosClient()
    .get<{message: string; result: Template}>(
      `${process.env.API_RISK_ASSESSMENT_URL}/templates/${id}`,
    );

  return data;
};

/**
 * Permanently deletes a template from the system.
 * This action cannot be undone. Consider using markTemplateAsArchived for soft deletion.
 *
 * @param {number} id - The unique identifier of the template to delete
 * @returns {Promise<any>} Promise that resolves to the deletion result
 * @throws {Error} When the API request fails or template is not found
 */
export const deleteTemplateById = async (id: number) => {
  const {data} = await httpService
    .getAxiosClient()
    .delete(`${process.env.API_RISK_ASSESSMENT_URL}/templates/${id}`);

  return data;
};

/**
 * Permanently deletes a risk assessment from the system.
 * This action cannot be undone and will remove all associated data.
 *
 * @param {number} id - The unique identifier of the risk assessment to delete
 * @returns {Promise<any>} Promise that resolves to the deletion result
 * @throws {Error} When the API request fails or risk assessment is not found
 */
export const deleteRiskById = async (id: number) => {
  const {data} = await httpService
    .getAxiosClient()
    .delete(`${process.env.API_RISK_ASSESSMENT_URL}/risks/${id}`);

  return data;
};

/**
 * Creates a new risk assessment template in the system.
 * Templates can be reused to create multiple risk assessments with similar structure.
 *
 * @param {any} payload - The template data including name, description, and configuration
 * @returns {Promise<any>} Promise that resolves to the created template data
 * @throws {Error} When the API request fails or validation errors occur
 */
export const createNewTemplate = async (payload: any) => {
  const {data} = await httpService
    .getAxiosClient()
    .post(`${process.env.API_RISK_ASSESSMENT_URL}/templates`, payload);

  return data;
};
/**
 * Updates an existing risk assessment template.
 * Allows modification of template properties while preserving the template ID.
 *
 * @param {number | string} id - The unique identifier of the template to update
 * @param {any} payload - The updated template data
 * @returns {Promise<any>} Promise that resolves to the updated template data
 * @throws {Error} When the API request fails, template is not found, or validation errors occur
 */
export const updateSavedTemplate = async (
  id: number | string,
  payload: any,
) => {
  const {data} = await httpService
    .getAxiosClient()
    .patch(`${process.env.API_RISK_ASSESSMENT_URL}/templates/${id}`, payload);

  return data;
};

/**
 * Retrieves the complete list of vessels available for risk assessment assignment.
 * Used to populate vessel selection dropdowns in risk assessment forms.
 *
 * @returns {Promise<VesselData[]>} Promise that resolves to the list of vessel data
 * @throws {Error} When the API request fails
 */
export const getVesselsList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<VesselData[]>(
      `${process.env.API_RISK_ASSESSMENT_URL}/vessel-ownership`,
    );
  return data;
};

/**
 * Retrieves the list of reporting offices available in the system.
 * Used for office selection and filtering in risk assessment management.
 *
 * @returns {Promise<OfficeItem[]>} Promise that resolves to the list of office items
 * @throws {Error} When the API request fails
 */
export const getOfficesList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<OfficeItem[]>(
      `${process.env.API_RISK_ASSESSMENT_URL}/reporting-office`,
    );
  return data;
};

/**
 * Retrieves a specific risk assessment by its unique identifier.
 * Returns complete risk assessment data including all associated details.
 *
 * @param {string} id - The unique identifier of the risk assessment
 * @returns {Promise<{message: string; result: RAItemFull}>} Promise that resolves to the full risk assessment data
 * @throws {Error} When the API request fails or risk assessment is not found
 */
export const getRiskById = async (id: string) => {
  const {data} = await httpService
    .getAxiosClient()
    .get<{message: string; result: RAItemFull}>(
      `${process.env.API_RISK_ASSESSMENT_URL}/risks/${id}`,
    );

  return data;
};

/**
 * Retrieves the crew list for a specific vessel, sorted by rank order.
 * Used for assigning crew members to risk assessments and selecting assessors.
 *
 * @param {number} vesselId - The unique identifier of the vessel
 * @returns {Promise<CrewMember[]>} Promise that resolves to the sorted crew list
 * @throws {Error} When the API request fails or vessel is not found
 */
export const getCrewList = async (vesselId: number) => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/crew-list`, {
      params: {
        vessel_id: vesselId,
      },
    });

  const sortedCrewList = (data.crewList as CrewMember[]).sort(
    (a, b) => a.seafarer_rank_sort_order - b.seafarer_rank_sort_order,
  );

  return sortedCrewList;
};

/**
 * Creates a new risk assessment in the system.
 * Initializes a new risk assessment with the provided data and configuration.
 *
 * @param {any} payload - The risk assessment data including vessel, hazards, and assessment details
 * @returns {Promise<any>} Promise that resolves to the created risk assessment data
 * @throws {Error} When the API request fails or validation errors occur
 */
export const createNewRA = async (payload: any) => {
  const {data} = await httpService
    .getAxiosClient()
    .post(`${process.env.API_RISK_ASSESSMENT_URL}/risks`, payload);

  return data;
};
/**
 * Updates an existing risk assessment with new data.
 * Allows modification of risk assessment properties while preserving the assessment ID.
 *
 * @param {number | string} id - The unique identifier of the risk assessment to update
 * @param {any} payload - The updated risk assessment data
 * @returns {Promise<any>} Promise that resolves to the updated risk assessment data
 * @throws {Error} When the API request fails, risk assessment is not found, or validation errors occur
 */
export const updateSavedRA = async (id: number | string, payload: any) => {
  const {data} = await httpService
    .getAxiosClient()
    .patch(`${process.env.API_RISK_ASSESSMENT_URL}/risks/${id}`, payload);

  return data;
};

/**
 * Retrieves the list of approvals required for a specific assessor.
 * Used to determine which risk assessments need approval from a particular user.
 *
 * @param {number} assessor - The unique identifier of the assessor/approver
 * @returns {Promise<IdName[]>} Promise that resolves to the list of items requiring approval
 * @throws {Error} When the API request fails or assessor is not found
 */
export const getApprovalsRequiredList = async (assessor: number) => {
  const {data} = await httpService
    .getAxiosClient()
    .get<IdName[]>(
      `${process.env.API_RISK_ASSESSMENT_URL}/approval-required/${assessor}`,
    );
  return data;
};
/**
 * Generates a PDF export of a specific risk assessment.
 * Creates a formatted PDF document containing all risk assessment details for reporting purposes.
 *
 * @param {number | string} id - The unique identifier of the risk assessment to export
 * @returns {Promise<any[]>} Promise that resolves to the PDF generation result
 * @throws {Error} When the API request fails or risk assessment is not found
 */
export const generatePDF = async (id: number | string) => {
  const {data} = await httpService
    .getAxiosClient()
    .get<any[]>(`${process.env.API_RISK_ASSESSMENT_URL}/risks/${id}/export`);
  return data;
};

/**
 * Retrieves the list of seafarer ranks available in the system.
 * Used for crew member rank selection and hierarchy management.
 *
 * @returns {Promise<any[]>} Promise that resolves to the list of seafarer ranks
 * @throws {Error} When the API request fails
 */
export const getSeafarerRanks = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<any[]>(
      `${process.env.API_RISK_ASSESSMENT_URL}/seafarer-lookup?values=ranks`,
    );
  return data;
};

/**
 * Retrieves office approvers with optional search functionality.
 * Used for finding and selecting users who can approve risk assessments.
 *
 * @param {string} [search] - Optional search term to filter approvers by name or office
 * @returns {Promise<OfficeApprover[]>} Promise that resolves to the list of office approvers
 * @throws {Error} When the API request fails
 */
export const getOfficeApprovers = async (search?: string) => {
  const {data} = await httpService
    .getAxiosClient()
    .get<OfficeApprover[]>(
      `${process.env.API_RISK_ASSESSMENT_URL}/users-with-office`,
      {params: {search: search || null}},
    );
  return data;
};

/**
 * Assigns approvers to a specific risk assessment.
 * Sets up the approval workflow by defining the order and hierarchy of approvers.
 *
 * @param {Object} body - The assignment data
 * @param {number} body.risk_id - The unique identifier of the risk assessment
 * @param {Array} body.approvers - Array of approver objects with keycloak_id, order, and optional rank
 * @param {string} body.approvers[].keycloak_id - The Keycloak ID of the approver
 * @param {number} body.approvers[].order - The approval order sequence
 * @param {string} [body.approvers[].rank] - Optional rank of the approver
 * @returns {Promise<any>} Promise that resolves to the assignment result
 * @throws {Error} When the API request fails or validation errors occur
 */
export const assignApproversToRA = async (body: {
  risk_id: number;
  approvers: Array<{
    keycloak_id: string;
    order: number;
    rank?: string;
  }>;
}) => {
  const {data} = await httpService
    .getAxiosClient()
    .post(`${process.env.API_RISK_ASSESSMENT_URL}/risk-approver`, body);
  return data;
};

/**
 * Sets the Risk Assessment (RA) level for a specific risk assessment.
 * Determines the approval level required based on the risk rating and organizational policies.
 *
 * @param {Object} body - The RA level data
 * @param {number} body.risk_id - The unique identifier of the risk assessment
 * @param {number} body.ra_level - The required RA level (typically 1-4)
 * @param {string} [body.approval_date] - Optional approval date in ISO format
 * @returns {Promise<{message: string}>} Promise that resolves to the operation result
 * @throws {Error} When the API request fails or invalid RA level is provided
 */
export const setRiskRaLevel = async (body: {
  risk_id: number;
  ra_level: number;
  approval_date?: string;
}) => {
  const {data} = await httpService
    .getAxiosClient()
    .put<{message: string}>(
      `${process.env.API_RISK_ASSESSMENT_URL}/risk-approver/ra-level`,
      body,
    );
  return data;
};

/**
 * Reassigns an approver in the approval workflow.
 * Replaces an existing approver with a new one while maintaining the approval order.
 *
 * @param {Object} body - The reassignment data
 * @param {number} body.risk_id - The unique identifier of the risk assessment
 * @param {number} body.approval_order - The order position of the approver to replace
 * @param {string} body.new_keycloak_id - The Keycloak ID of the new approver
 * @param {string} [body.rank] - Optional rank of the new approver
 * @returns {Promise<{message: string}>} Promise that resolves to the reassignment result
 * @throws {Error} When the API request fails or approver is not found
 */
export const reAssignApprover = async (body: {
  risk_id: number;
  approval_order: number;
  new_keycloak_id: string;
  rank?: string;
}) => {
  const {data} = await httpService
    .getAxiosClient()
    .post<{message: string}>(
      `${process.env.API_RISK_ASSESSMENT_URL}/risk-approver/replace`,
      body,
    );
  return data;
};

/**
 * Approves or rejects a risk assessment.
 * Allows authorized users to approve or reject risk assessments with optional comments.
 *
 * @param {Object} params - The approval/rejection parameters
 * @param {number} params.risk_id - The unique identifier of the risk assessment
 * @param {ApprovalStatus} params.status - The approval status (APPROVED or REJECTED)
 * @param {string} [params.approval_date] - Optional approval date in ISO format
 * @param {string} params.keycloak_id - The Keycloak ID of the approver
 * @param {string} [params.message] - Optional comment or reason for the decision
 * @returns {Promise<{message: string}>} Promise that resolves to the approval result
 * @throws {Error} When the API request fails or user is not authorized
 */
export const approveOrRejectRA = async (params: {
  risk_id: number;
  status: ApprovalStatus;
  approval_date?: string;
  keycloak_id: string;
  message?: string;
}) => {
  const {risk_id, ...body} = params;
  const {data} = await httpService
    .getAxiosClient()
    .put<{message: string}>(
      `${process.env.API_RISK_ASSESSMENT_URL}/risk-approver/${risk_id}/status`,
      body,
    );
  return data;
};
