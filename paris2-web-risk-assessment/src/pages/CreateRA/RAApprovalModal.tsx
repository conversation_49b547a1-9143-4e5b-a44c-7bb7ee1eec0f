import React, {useState} from 'react';
import {<PERSON><PERSON>, But<PERSON>} from 'react-bootstrap';
import {toast} from 'react-toastify';
import CustomDatePicker from '../../components/CustomDatePicker';
import {InputComponent} from '../../components/InputComponent';
import {ApprovalStatus} from '../../enums';
import {getErrorMessage} from '../../utils/common';

import '../../styles/components/re-assign-approver-modal.scss';

export type ApprovalOperationType = 'approve' | 'approveWithComment' | 'reject';

export const operationTypeToApprovalStatus: Record<
  ApprovalOperationType,
  ApprovalStatus
> = {
  approve: ApprovalStatus.APPROVED,
  approveWithComment: ApprovalStatus.CONDITIONALLY_APPROVED,
  reject: ApprovalStatus.REJECTED,
};

export interface RAApprovalModalProps {
  onConfirm: (params: {
    operationType: ApprovalOperationType;
    actionDate?: Date;
    comment?: string;
  }) => Promise<{message?: string} | void>;
  operationType?: ApprovalOperationType;
  trigger: React.ReactElement;
  reviewIndex?: number;
}

const RAApprovalModal: React.FC<RAApprovalModalProps> = ({
  onConfirm,
  operationType = 'approve',
  trigger,
  reviewIndex = 0,
}) => {
  const [show, setShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [actionDate, setActionDate] = useState<Date | undefined>(undefined);
  const [comment, setComment] = useState<string>('');

  const handleTriggerClick = () => {
    setShow(true);
  };

  const handleClose = () => {
    setShow(false);
    setActionDate(undefined);
    setComment('');
    setIsLoading(false);
  };

  const handleConfirm = async () => {
    try {
      if (operationType === 'reject') {
        toast.success('RA Rejected');
      } else if (operationType === 'approve') {
        toast.success('RA Approved');
      } else {
        toast.success('RA Approved with Condition');
      }
    } catch (error) {
      toast.error(
        getErrorMessage(
          error,
          'Failed to approve the RA. Please try again later.',
        ),
      );
    } finally {
      setIsLoading(false);
    }

    handleClose();
  };

  let disableButtons: boolean =
    operationType === 'approve' ? !actionDate : false;
  if (operationType === 'approveWithComment') {
    disableButtons = !actionDate || !comment.trim();
  }
  if (operationType === 'reject') {
    disableButtons = !actionDate || !comment.trim();
  }

  return (
    <>
      {trigger &&
        React.cloneElement(trigger, {
          onClick: handleTriggerClick,
        })}

      <Modal
        show={show}
        onHide={handleClose}
        size="lg"
        backdrop="static"
        className="reassign-approver-modal"
        dialogClassName="top-modal"
      >
        <Modal.Header>
          <Modal.Title>
            {operationType === 'reject' ? 'Rejecting' : 'Approving'} Risk
            Assessment
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {operationType === 'reject' && (
            <div className="text-warning red-text-warning">
              <span style={{fontWeight: 600}}>
                Do you really want to Reject? This action is not reversible.
              </span>
              <br />
              Once confirmed, the Risk Assessment will be marked as Rejected and
              no further changes will be allowed
            </div>
          )}
          {operationType === 'approve' && reviewIndex === 2 && (
            <div className="text-warning yellow-text-warning">
              <span style={{fontWeight: 600}}>
                Do you really want to give Final Approval? This action is not
                reversible.
              </span>
              <br />
              Once confirmed, the Risk Assessment will be marked as Approved and
              no further changes will be allowed
            </div>
          )}
          {operationType === 'approveWithComment' && reviewIndex === 2 && (
            <div className="text-warning yellow-text-warning">
              <span style={{fontWeight: 600}}>
                Do you really want to give Final Approval with Condition? This
                action is not reversible.
              </span>
              <br />
              Once confirmed, the Risk Assessment will be marked as Approved
              with Condition and no further changes will be allowed
            </div>
          )}

          {operationType === 'reject' ? (
            <>
              <CustomDatePicker
                isRequired={true}
                minDate={undefined}
                label="Rejection Date"
                value={actionDate}
                onChange={date => setActionDate(date)}
                placeholder="Select Date"
                controlId="rejection_date"
                errorMsg="This is a mandatory field. Please fill to process."
              />
              <InputComponent
                label="Reason for Rejecting RA"
                type="textarea"
                onChange={(
                  e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
                ) => {
                  setComment(e.target.value);
                }}
                maxLength={255}
                rows={4}
                showMaxLength
                value={comment}
                name="reason_for_rejection"
                placeholder="Type the Reason for Rejection"
              />
            </>
          ) : (
            <>
              <CustomDatePicker
                isRequired={true}
                minDate={undefined}
                label="Approval Date"
                value={actionDate}
                onChange={date => setActionDate(date)}
                placeholder="Select Date"
                controlId="approval_date"
                errorMsg="This is a mandatory field. Please fill to process."
                className={operationType === 'approveWithComment' ? 'mb-3' : ''}
              />

              {operationType === 'approveWithComment' && (
                <InputComponent
                  label="Condition for Approval"
                  type="textarea"
                  onChange={(
                    e: React.ChangeEvent<
                      HTMLInputElement | HTMLTextAreaElement
                    >,
                  ) => {
                    setComment(e.target.value);
                  }}
                  maxLength={255}
                  rows={4}
                  showMaxLength
                  value={comment}
                  name="condition_for_approval"
                  placeholder="Type the Condition for Approval"
                />
              )}
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="primary"
            className="me-2 fs-14"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="secondary"
            className="me-2 fs-14"
            onClick={handleConfirm}
            disabled={isLoading || disableButtons}
          >
            {operationType === 'reject' ? 'Reject' : 'Approve'}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default RAApprovalModal;
