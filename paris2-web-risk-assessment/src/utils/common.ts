import _ from 'lodash';
import moment from 'moment';
import {AxiosError} from 'axios';
import {RaLevel, RAStatus} from '../enums';

/**
 * Recursively cleans an object by removing null, undefined, empty arrays, and empty objects.
 * Uses lodash's omitBy to filter out unwanted values while preserving nested structure.
 *
 * @param {Record<string, unknown>} value - The object to clean
 * @returns {Record<string, unknown>} A new object with empty values removed
 *
 * @example
 * const dirty = { name: '<PERSON>', age: null, hobbies: [], address: { street: '', city: 'NYC' } };
 * const clean = cleanObject(dirty);
 * // Result: { name: '<PERSON>', address: { city: 'NYC' } }
 */
export const cleanObject = (
  value: Record<string, unknown>,
): Record<string, unknown> => {
  return _.omitBy(value, item => {
    if (Array.isArray(item)) {
      return item.length === 0;
    }
    if (typeof item === 'object' && item !== null) {
      return (
        Object.keys(cleanObject(item as Record<string, unknown>)).length === 0
      );
    }
    return _.isNil(item);
  });
};

/**
 * Parses and formats a date string or Date object using moment.js.
 * Returns undefined for invalid dates to maintain type safety.
 *
 * @param {string | Date | null | undefined} [date] - The date to parse and format
 * @param {string} [format='DD MMM YYYY'] - The desired output format
 * @returns {string | undefined} Formatted date string or undefined if invalid
 *
 * @example
 * parseDate('2023-12-25') // Returns: '25 Dec 2023'
 * parseDate(new Date(), 'YYYY-MM-DD') // Returns: '2023-12-25'
 * parseDate(null) // Returns: undefined
 */
export const parseDate = (
  date: string | Date | null | undefined = undefined,
  format = 'DD MMM YYYY',
): string | undefined => {
  return date && moment(date).isValid()
    ? moment(date).clone().format(format)
    : undefined;
};

/**
 * Creates date range filter parameters for API queries.
 * Generates start_date and end_date parameters for a given field.
 *
 * @param {string} fieldKey - The base field name for the date range
 * @param {[string | null, string | null] | null} [range] - Array with start and end dates
 * @returns {Record<string, string>} Object with date range filter parameters
 *
 * @example
 * getDateRangeFilters('created_at', ['2023-01-01', '2023-12-31'])
 * // Returns: { 'created_at[start_date]': '2023-01-01', 'created_at[end_date]': '2023-12-31' }
 */
export const getDateRangeFilters = (
  fieldKey: string,
  range?: [string | null, string | null] | null,
): Record<string, string> => {
  const filters: Record<string, string> = {};
  if (range?.[0]) filters[`${fieldKey}[start_date]`] = range[0];
  if (range?.[1]) filters[`${fieldKey}[end_date]`] = range[1];
  return filters;
};

/**
 * Mapping of vessel status codes to their display labels.
 * Used for filtering and displaying vessel status in the UI.
 */
export const vesselStatusAndLabelName: Record<string, string> = {
  active: 'Active Vessels',
  pending_handover: 'Pending Handover Vessels',
  handed_over: 'Handed Over Vessels',
};

/**
 * Mapping of assessor types to their display labels.
 * Defines whether the assessment is done by office or vessel personnel.
 */
export const assessorLabel: Record<number, string> = {
  1: 'Office',
  2: 'Vessel',
};

/**
 * Returns the value if it is not empty, otherwise returns the default placeholder '---'.
 * Empty values are: null, undefined, empty string, or string with only whitespace.
 *
 * @template T - The type of the value being checked
 * @param {T} value - The value to check
 * @param {string} [placeholder='---'] - The placeholder to use if value is empty
 * @returns {T | string} The original value or the placeholder string
 *
 * @example
 * withDefault('Hello') // Returns: 'Hello'
 * withDefault(null) // Returns: '---'
 * withDefault('', 'N/A') // Returns: 'N/A'
 * withDefault(123) // Returns: 123
 */
export function withDefault<T = any>(
  value: T,
  placeholder = '---',
): T | string {
  if (
    value === null ||
    value === undefined ||
    (typeof value === 'string' && value.trim() === '')
  ) {
    return placeholder;
  }
  return value;
}

/**
 * Mapping of Risk Assessment levels to their display labels.
 * Used for showing RA level classifications in the UI.
 */
export const raLevelLabel: Record<number, string> = {
  [RaLevel.CRITICAL]: 'Critical',
  [RaLevel.LEVEL_1_RA]: 'Level 1 RA',
  [RaLevel.ROUTINE]: 'Routine',
  [RaLevel.SPECIAL]: 'Special',
};

/**
 * Mapping of Risk Assessment levels to their corresponding color themes.
 * Used for visual differentiation of RA levels in badges and indicators.
 */
export const raLevelColor: Record<number, 'red' | 'blue' | 'yellow' | 'green'> =
  {
    [RaLevel.CRITICAL]: 'red',
    [RaLevel.LEVEL_1_RA]: 'blue',
    [RaLevel.ROUTINE]: 'blue',
    [RaLevel.SPECIAL]: 'red',
  };

/**
 * Mapping of Risk Assessment status codes to their display labels.
 * Used for showing current status of risk assessments in the UI.
 */
export const raStatusLabel: Record<number, string> = {
  [RAStatus.DRAFT]: 'Draft',
  [RAStatus.PUBLISHED]: 'Published',
  [RAStatus.APPROVED]: 'Approved',
  [RAStatus.REJECTED]: 'Rejected',
  [RAStatus.INACTIVE]: 'Inactive',
};

/**
 * Extracts a user-friendly error message from various error types.
 * Handles both Axios errors and generic JavaScript errors with fallback messaging.
 *
 * @param {unknown} error - The error object to extract message from
 * @param {string} [fallbackMessage='An unexpected error occurred. Please try again later.'] - Default message if extraction fails
 * @returns {string} A user-friendly error message
 *
 * @example
 * try {
 *   await riskyOperation();
 * } catch (error) {
 *   const message = getErrorMessage(error, 'Operation failed');
 *   toast.error(message);
 * }
 */
export const getErrorMessage = (
  error: unknown,
  fallbackMessage = 'An unexpected error occurred. Please try again later.',
): string => {
  if (error instanceof AxiosError && error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return fallbackMessage;
};

/**
 * Mapping of Risk Assessment status codes to their uppercase string labels.
 * Used for API communication and consistent status representation.
 */
export const raStatusToLabel: Record<number, string> = {
  [RAStatus.DRAFT]: 'DRAFT',
  [RAStatus.PUBLISHED]: 'PUBLISHED',
  [RAStatus.APPROVED]: 'APPROVED',
  [RAStatus.REJECTED]: 'REJECTED',
  [RAStatus.INACTIVE]: 'INACTIVE',
};

/**
 * Reverse mapping of Risk Assessment status labels to their numeric values.
 * Used for converting string status labels back to enum values.
 */
export const raStatusLabelToValue: Record<string, number> = {
  DRAFT: RAStatus.DRAFT,
  PUBLISHED: RAStatus.PUBLISHED,
  APPROVED: RAStatus.APPROVED,
  REJECTED: RAStatus.REJECTED,
  INACTIVE: RAStatus.INACTIVE,
};
