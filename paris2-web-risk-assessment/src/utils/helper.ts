import {RaLevel, TemplateFormStatus} from '../enums';
import {
  Parameter,
  TemplateForm,
  TemplateFormCategory,
  TemplateFormHazard,
  TemplateFormJobResidualRiskRating,
} from '../types/template';
import {v4 as uuidv4} from 'uuid';
import * as _ from 'lodash';
import {riskMatrix} from '../constants/riskMatrix';
import {
  RiskForm,
  RiskCategory,
  RiskHazard,
  RiskFormJob,
  RiskTaskReliabilityAssessment,
  RiskJobResidualRiskRating,
  RiskJobInitialRiskRating,
  RiskTeamMember,
  RAItemFull,
} from '../types/risk';
import {raStatusToLabel} from './common';
import {RiskParameter} from '../types';

/**
 * Input type for risk parameter transformation.
 * Represents the structure of risk parameter data from the API.
 */
type RiskParameterTypeInput = {
  id: number;
  name: string;
  parameters: {id: number; name: string}[];
};

/**
 * Output type for grouped options used in UI components.
 * Represents the transformed structure for checkbox grids and similar components.
 */
type GroupedOption = {
  id: number;
  label: string;
  options: {id: number; label: string}[];
  columns: number;
};

/**
 * Transforms risk parameter types into grouped options for UI rendering.
 * Converts API response format into a structure suitable for grouped checkbox components.
 *
 * @param {RiskParameterTypeInput[]} riskParameterType - Array of risk parameter types from API
 * @param {number} [columns=3] - Number of columns for the UI grid layout
 * @returns {GroupedOption[]} Array of grouped options for UI components
 *
 * @example
 * const apiData = [
 *   { id: 1, name: 'Safety', parameters: [{ id: 1, name: 'PPE' }, { id: 2, name: 'Training' }] }
 * ];
 * const options = generateGroupedOptions(apiData, 2);
 * // Returns: [{ id: 1, label: 'SAFETY', options: [{ id: 1, label: 'PPE' }, ...], columns: 2 }]
 */
export function generateGroupedOptions(
  riskParameterType: RiskParameterTypeInput[],
  columns = 3,
): GroupedOption[] {
  return riskParameterType.map(group => ({
    id: group.id,
    label: group.name.toUpperCase(),
    options: group.parameters.map(opt => ({
      id: opt.id,
      label: opt.name,
    })),
    columns,
  }));
}

/**
 * Utility to remove and reindex job state objects after a job is deleted.
 * Maintains sequential indexing by removing the specified index and reordering remaining items.
 *
 * @template T - Type extending Record<number, any>
 * @param {T} state - The current state object with numeric keys
 * @param {number} idx - The index to remove from the state
 * @returns {T} New state object with the specified index removed and remaining items reindexed
 *
 * @example
 * const currentState = { 0: { name: 'Job A' }, 1: { name: 'Job B' }, 2: { name: 'Job C' } };
 * const newState = removeAndReindexJobState(currentState, 1);
 * // Returns: { 0: { name: 'Job A' }, 1: { name: 'Job C' } }
 */
export function removeAndReindexJobState<T extends Record<number, any>>(
  state: T,
  idx: number,
): T {
  const newState: T = {} as T;
  Object.keys(state)
    .map(Number)
    .filter(i => i !== idx)
    .sort((a, b) => a - b)
    .forEach(i => {
      newState[i < idx ? i : i - 1] = state[i];
    });
  return newState;
}

/**
 * Comprehensive form parameter handler that cleans and validates payload data.
 * Removes empty fields, processes nested objects, and ensures data integrity
 * before sending to the API. Handles both template and risk assessment forms.
 *
 * @param {any} payload - The form payload to process and clean
 * @returns {any} Cleaned and processed payload ready for API submission
 *
 * @description
 * This function performs the following operations:
 * - Removes empty category, hazard, and job arrays
 * - Cleans up parameter arrays by filtering valid entries
 * - Processes risk ratings and reliability assessments
 * - Removes metadata fields like id, timestamps, and user info
 * - Handles special cases for draft submissions
 *
 * @example
 * const rawPayload = {
 *   template_category: { category_id: [] },
 *   worst_case_scenario: '',
 *   parameters: [{ parameter_id: [1, 2] }, { parameter_id: [] }]
 * };
 * const cleaned = formParameterHandler(rawPayload);
 * // Returns payload with empty fields removed and parameters filtered
 */
export const formParameterHandler = (payload: any) => {
  if (!payload?.template_category?.category_id?.length) {
    delete payload.template_category;
  }
  if (!payload?.risk_category?.category_id?.length) {
    delete payload.risk_category;
  }
  if (
    !payload?.template_hazard?.hazard_id?.length &&
    !payload?.template_hazard?.is_other
  ) {
    delete payload.template_hazard;
  }
  if (!payload?.template_job?.[0]?.job_step?.length) {
    delete payload.template_job;
  }
  if (
    !payload?.risk_category?.[0]?.isOther &&
    payload?.risk_category?.value === ''
  ) {
    delete payload.risk_category.value;
  }
  if (
    !payload?.template_category?.[0]?.isOther &&
    payload?.template_category?.value === ''
  ) {
    delete payload.template_category.value;
  }
  if (
    !payload?.risk_hazard?.[0]?.isOther &&
    payload?.risk_hazard?.value === ''
  ) {
    delete payload.risk_hazard.value;
  }
  if (
    !payload?.template_hazard?.[0]?.isOther &&
    payload?.template_hazard?.value === ''
  ) {
    delete payload.template_hazard.value;
  }

  // Handle risk_hazard deletion if hazard_id is empty and is_other is false
  if (
    !payload?.risk_hazard?.hazard_id?.length &&
    !payload?.risk_hazard?.is_other
  ) {
    delete payload.risk_hazard;
  }

  // Handle empty risk form fields for save as draft
  if (!payload?.worst_case_scenario?.length) {
    delete payload.worst_case_scenario;
  }
  if (!payload?.recovery_measures?.length) {
    delete payload.recovery_measures;
  }
  payload.parameters = (payload?.parameters as Parameter[] | undefined)?.filter(
    (param: Parameter) =>
      (param?.parameter_id && param.parameter_id.length) || param?.is_other,
  );

  if (Array.isArray(payload.template_job)) {
    payload.template_job = payload.template_job.map(
      ({
        id,
        job_id,
        template_job_residual_risk_rating,
        template_job_initial_risk_rating,
        job_close_out_date,
        job_close_out_responsibility_id,
        ...rest
      }: {
        job_id?: any;
        [key: string]: any;
      }) => ({
        ...rest,
        ...(Array.isArray(template_job_residual_risk_rating) && {
          template_job_residual_risk_rating:
            template_job_residual_risk_rating.map(
              ({rating, parameter_type_id, reason}: any) => {
                const base = {rating, parameter_type_id};
                return reason ? {...base, reason} : base;
              },
            ),
        }),
        ...(Array.isArray(template_job_initial_risk_rating) && {
          template_job_initial_risk_rating:
            template_job_initial_risk_rating.map(
              ({rating, parameter_type_id}: any) => ({
                rating,
                parameter_type_id,
              }),
            ),
        }),
      }),
    );
  }
  if (payload.template_task_reliability_assessment?.length) {
    payload.template_task_reliability_assessment =
      payload.template_task_reliability_assessment.map((item: any) => {
        const {condition} = item;
        const newItem = {
          task_reliability_assessment_answer:
            item?.task_reliability_assessment_answer ||
            item?.task_reliability_assessmen ||
            '',
          task_reliability_assessment_id:
            item?.task_reliability_assessment_id || item?.id || null,
        };
        return !condition?.length ? newItem : {...newItem, condition};
      });
  }
  if (payload.risk_task_reliability_assessment?.length) {
    payload.risk_task_reliability_assessment =
      payload.risk_task_reliability_assessment.map((item: any) => {
        const {condition} = item;
        const newItem = {
          task_reliability_assessment_answer:
            item?.task_reliability_assessment_answer ||
            item?.task_reliability_assessmen ||
            '',
          task_reliability_assessment_id:
            item?.task_reliability_assessment_id || item?.id || null,
        };
        return !condition?.length
          ? newItem
          : {...newItem, condition, task_reliability_assessment_answer: 'Yes'};
      });
  }
  // Handle RiskForm hazard
  if (payload?.risk_hazard) {
    if (!payload?.risk_hazard?.is_other && payload?.risk_hazard?.value === '') {
      delete payload.risk_hazard.value;
    }
  }

  // Handle risk jobs
  if (Array.isArray(payload.risk_job)) {
    payload.risk_job = payload.risk_job.map(
      ({
        risk_job_residual_risk_rating,
        risk_job_initial_risk_rating,
        job_close_out_date,
        job_close_out_responsibility_id,
        ...rest
      }: {
        [key: string]: any;
      }) => {
        const processedJob: any = {...rest};

        // Handle risk ratings
        if (Array.isArray(risk_job_residual_risk_rating)) {
          processedJob.risk_job_residual_risk_rating =
            risk_job_residual_risk_rating.map(
              ({rating, parameter_type_id, reason}: any) => {
                const base = {rating, parameter_type_id};
                return reason ? {...base, reason} : base;
              },
            );
        }

        if (Array.isArray(risk_job_initial_risk_rating)) {
          processedJob.risk_job_initial_risk_rating =
            risk_job_initial_risk_rating.map(
              ({rating, parameter_type_id}: any) => ({
                rating,
                parameter_type_id,
              }),
            );
        }

        // Only include job_close_out_date if it's not empty
        if (job_close_out_date && job_close_out_date.length > 0) {
          processedJob.job_close_out_date = job_close_out_date;
        }

        // Only include job_close_out_responsibility_id if it's not empty
        if (
          job_close_out_responsibility_id &&
          job_close_out_responsibility_id.length > 0
        ) {
          processedJob.job_close_out_responsibility_id =
            job_close_out_responsibility_id;
        }

        return processedJob;
      },
    );

    // Filter out risk jobs that have empty required fields for save as draft
    payload.risk_job = payload.risk_job.filter((job: any) => {
      // Keep job if at least job_step has content (minimum requirement)
      return job.job_step && job.job_step.length > 0;
    });

    // If no valid risk jobs remain, delete the entire risk_job array
    if (payload.risk_job.length === 0) {
      delete payload.risk_job;
    }
  }

  // Handle risk task reliability assessment
  if (payload.risk_task_reliability_assessment?.length) {
    payload.risk_task_reliability_assessment =
      payload.risk_task_reliability_assessment.map((item: any) => {
        const {condition} = item;
        const newItem = {
          task_reliability_assessment_answer:
            item?.task_reliability_assessment_answer ||
            item?.task_reliability_assessmen ||
            '',
          task_reliability_assessment_id:
            item?.task_reliability_assessment_id || item?.id || null,
        };
        return !condition?.length ? newItem : {...newItem, condition};
      });
  }

  // Remove 'value' key if 'is_other' is false in each parameter
  if (Array.isArray(payload.parameters)) {
    payload.parameters = payload.parameters.map((param: Parameter) => {
      if (param?.parameter_id?.length) {
        param.parameter_id = _.uniq(param.parameter_id);
      }
      if (param && param.is_other === false) {
        const {value, ...rest} = param;
        return rest;
      }

      return param;
    });
  }
  if (!payload?.approval_required?.length) {
    delete payload.approval_required;
  }
  if (payload?.id) {
    delete payload.id;
  }
  if (!payload?.task_duration?.length) {
    delete payload.task_duration;
  }

  if (payload?.risk_team_member?.length) {
    payload.risk_team_member = payload.risk_team_member.map(
      ({id, risk_id, ...rest}: any) => rest,
    );
  }
  if (!payload?.assessor) {
    delete payload.assessor;
  }
  if (!payload?.date_risk_assessment) {
    delete payload.date_risk_assessment;
  }
  if (!payload?.vessel_ownership_id) {
    delete payload.vessel_ownership_id;
  }
  if (!payload?.office_id) {
    delete payload.office_id;
    delete payload.office_name;
  }
  if (payload?.updated_at) {
    delete payload.updated_at;
  }
  if (payload?.created_by) {
    delete payload.created_by;
  }
  if (payload?.updated_by) {
    delete payload.updated_by;
  }
  if ('vessel_id' in payload) {
    delete payload.vessel_id;
  }
  if ('risk_approver' in payload) {
    delete payload.risk_approver;
  }
  if (
    'risk_team_member' in payload &&
    Array.isArray(payload.risk_team_member)
  ) {
    payload.risk_team_member = payload.risk_team_member.map(
      (member: {[s: string]: unknown} | ArrayLike<unknown>) => {
        const cleaned: any = {};
        Object.entries(member).forEach(([key, value]) => {
          if (value !== null && key !== 'status' && value !== '') {
            cleaned[key] = value;
          }
        });
        return cleaned;
      },
    );
  }

  return payload;
};

/**
 * Maps incoming parameter data to the required Parameter[] format.
 * Groups parameters by type and handles 'other' parameter values.
 *
 * @param {any[]} input - Array of parameter data from the API
 * @returns {Parameter[]} Array of formatted Parameter objects
 *
 * @description
 * Groups parameters by parameterType.id and creates consolidated Parameter objects
 * with proper handling of custom 'other' parameter values.
 */
function mapParameters(input: any[]): Parameter[] {
  if (!Array.isArray(input)) return [];
  return _.chain(input)
    .groupBy(item => item?.parameterType?.id) // TBC parameter_type_id
    .map(items => {
      const hasOtherValue =
        items.filter(item => item.parameter_is_other)?.[0]?.value ?? '';
      return {
        is_other: !!hasOtherValue,
        parameter_type_id: items[0]?.parameterType?.id, // TBC parameter_type_id
        parameter_id: _.compact(_.map(items, 'parameter.id')) ?? [],
        value: hasOtherValue,
      };
    })
    .value();
}
/**
 * Maps hazard data from API format to TemplateFormHazard structure.
 * Separates predefined hazards from custom 'other' hazard values.
 *
 * @param {any[]} input - Array of hazard data from the API
 * @returns {TemplateFormHazard} Formatted hazard object with IDs and custom values
 *
 * @description
 * Processes hazard data by:
 * - Extracting custom hazard values from 'other' entries
 * - Collecting predefined hazard IDs from hazard_detail entries
 * - Setting appropriate flags for custom vs predefined hazards
 */
function mapHazards(input: any[]): TemplateFormHazard {
  const hasOtherValue =
    input?.filter(item => item.hazard_category_is_other)?.[0]?.value ?? '';
  return {
    is_other: !!hasOtherValue,
    value: hasOtherValue,
    hazard_id:
      _.compact(
        input
          ?.filter(
            item => !item?.hazard_category_is_other && item?.hazard_detail,
          )
          .map(item => item?.hazard_detail?.id),
      ) ?? [],
  };
}

/**
 * Maps category data from API format to TemplateFormCategory structure.
 * Handles both predefined categories and custom 'other' category values.
 *
 * @param {any[]} [input] - Array of category data from the API
 * @returns {TemplateFormCategory} Formatted category object with IDs and custom values
 *
 * @description
 * Processes category data by:
 * - Extracting custom category values from 'other' entries
 * - Collecting predefined category IDs from category entries
 * - Providing default empty structure if no input provided
 */
function mapCategories(input?: any[]): TemplateFormCategory {
  if (!Array.isArray(input)) {
    return {
      is_other: false,
      value: '',
      category_id: [],
    };
  }

  const otherItem = input.find(item => item.category_is_other);
  const hasOtherValue = otherItem?.value ?? '';

  return {
    is_other: !!hasOtherValue,
    value: hasOtherValue,
    category_id: input
      .filter(item => !item.category_is_other)
      .map(item => item.category?.id)
      .filter(Boolean),
  };
}

/**
 * Creates a TemplateForm object from API data or initializes with defaults.
 * Transforms raw API response into the structured format required by the form.
 *
 * @param {any} [data] - Optional API data to transform into TemplateForm
 * @returns {TemplateForm} Complete TemplateForm object with all required fields
 *
 * @description
 * Converts API response data into a properly structured TemplateForm object:
 * - Maps basic fields with fallback to empty strings
 * - Processes complex nested objects (categories, hazards, parameters)
 * - Generates unique job IDs for template jobs
 * - Provides default job structure if none exists
 * - Preserves metadata fields (created_by, updated_by, etc.)
 *
 * @example
 * const apiData = { task_requiring_ra: 'Engine Maintenance', template_job: [...] };
 * const form = createFormFromData(apiData);
 * // Returns complete TemplateForm with all required fields populated
 */
export const createFormFromData = (data?: any): TemplateForm => {
  return {
    task_requiring_ra: data?.task_requiring_ra ?? '',
    task_duration: data?.task_duration ?? '',
    task_alternative_consideration: data?.task_alternative_consideration ?? '',
    task_rejection_reason: data?.task_rejection_reason ?? '',
    worst_case_scenario: data?.worst_case_scenario ?? '',
    recovery_measures: data?.recovery_measures ?? '',
    status: TemplateFormStatus.DRAFT,

    parameters: data?.template_parameter?.length
      ? mapParameters(data?.template_parameter)
      : [],

    template_category: mapCategories(data?.template_category),
    template_job: data?.template_job?.length
      ? data?.template_job?.map((job: any) => ({
          ...job,
          job_id: uuidv4(),
        }))
      : [
          {
            job_id: uuidv4(),
            job_step: '',
            job_hazard: '',
            job_nature_of_risk: '',
            job_existing_control: '',
            job_additional_mitigation: '',
            job_close_out_date: '',
            job_close_out_responsibility_id: '',
            template_job_initial_risk_rating: [],
            template_job_residual_risk_rating: [],
          },
        ],
    template_hazard: mapHazards(data?.template_hazards),
    template_task_reliability_assessment:
      data?.template_task_reliability_assessment ?? [],
    template_keyword: data?.template_keyword ?? ['test'],
    updated_at: data?.updated_at ?? undefined,
    updated_by: data?.updated_by ?? undefined,
    created_by: data?.created_by ?? undefined,
  };
};

/**
 * Validates if a required field is empty or contains only whitespace.
 *
 * @param {string} [value] - The field value to validate
 * @returns {boolean} True if field is empty/invalid, false if valid
 *
 * @example
 * validateRequiredField('') // Returns: true (invalid)
 * validateRequiredField('  ') // Returns: true (invalid)
 * validateRequiredField('Valid input') // Returns: false (valid)
 */
export const validateRequiredField = (value?: string): boolean => {
  return !value?.trim();
};

/**
 * Validates that all task reliability assessments have answers.
 *
 * @param {any[]} assessments - Array of task reliability assessment objects
 * @returns {boolean} True if invalid (missing answers), false if all valid
 *
 * @description
 * Checks that:
 * - Assessment array is not empty
 * - Each assessment has a task_reliability_assessment_answer
 *
 * @example
 * const assessments = [{ task_reliability_assessment_answer: 'Yes' }];
 * validateTaskReliabilityAnswers(assessments) // Returns: false (valid)
 */
export const validateTaskReliabilityAnswers = (assessments: any[]): boolean => {
  return (
    !assessments.length ||
    assessments.some((item: any) => !item.task_reliability_assessment_answer)
  );
};

/**
 * Extracts the appropriate assessment array from either TemplateForm or RiskForm.
 *
 * @param {TemplateForm | RiskForm} form - The form object to extract assessments from
 * @returns {any[]} Array of task reliability assessments
 *
 * @description
 * Handles both form types by checking for the appropriate assessment property:
 * - TemplateForm: uses template_task_reliability_assessment
 * - RiskForm: uses risk_task_reliability_assessment
 */
export const getAssessmentArray = (form: TemplateForm | RiskForm): any[] => {
  if ('template_task_reliability_assessment' in form) {
    return Array.isArray(form.template_task_reliability_assessment)
      ? form.template_task_reliability_assessment
      : [];
  } else {
    return Array.isArray(form.risk_task_reliability_assessment)
      ? form.risk_task_reliability_assessment
      : [];
  }
};

/**
 * Calculates the overall risk rating based on task reliability answers and job risk ratings.
 * Uses a combination of assessment answers and risk matrix calculations to determine the final rating.
 *
 * @param {TemplateForm | RiskForm} form - The form containing assessment and job data
 * @returns {string} Risk rating classification ('High', 'Medium', or 'Low')
 *
 * @description
 * Risk rating calculation logic:
 * 1. If any task reliability assessment answer is 'No', returns 'High'
 * 2. Calculates maximum rate from all job residual risk ratings
 * 3. Maps the maximum rate to risk classification using risk matrix
 * 4. Defaults to 'Medium' if no specific rating can be determined
 *
 * Supports both TemplateForm and RiskForm structures by detecting the appropriate
 * properties for assessments and jobs.
 *
 * @example
 * const form = {
 *   template_task_reliability_assessment: [{ task_reliability_assessment_answer: 'Yes' }],
 *   template_job: [{ template_job_residual_risk_rating: [{ rating: 'A1' }] }]
 * };
 * const rating = calculateRiskRating(form); // Returns: 'Low', 'Medium', or 'High'
 */
export const calculateRiskRating = (form: TemplateForm | RiskForm): string => {
  // Support both TemplateForm and RiskForm for assessment array
  let assessments: any[] = [];

  if ('template_task_reliability_assessment' in form) {
    if (Array.isArray(form.template_task_reliability_assessment)) {
      assessments = form.template_task_reliability_assessment;
    }
  } else if (Array.isArray(form?.risk_task_reliability_assessment)) {
    assessments = form?.risk_task_reliability_assessment;
  }

  const answers = assessments.map(
    (item: any) => item.task_reliability_assessment_answer,
  );

  // Existing logic: if any answer is 'No', return 'High'
  const anyNo = answers.some(ans => ans === 'No');
  if (anyNo) return 'High';

  // New logic: calculate max rate from all jobs' RRRs
  let maxRate = 0;

  // Handle jobs for both TemplateForm and RiskForm
  let jobs: any[] = [];

  if ('template_job' in form) {
    if (Array.isArray(form.template_job)) {
      jobs = form.template_job;
    }
  } else if (Array.isArray(form?.risk_job)) {
    jobs = form?.risk_job;
  }

  jobs.forEach(job => {
    // For TemplateForm
    if ('template_job_residual_risk_rating' in job) {
      if (Array.isArray(job.template_job_residual_risk_rating)) {
        job.template_job_residual_risk_rating.forEach(
          (rrr: TemplateFormJobResidualRiskRating) => {
            if (rrr.rating) {
              const matrixItem = riskMatrix.find(
                item => item.value === rrr.rating,
              );
              if (matrixItem && typeof matrixItem.rate === 'number') {
                if (matrixItem.rate > maxRate) {
                  maxRate = matrixItem.rate;
                }
              }
            }
          },
        );
      }
    }
    // For RiskForm
    if ('risk_job_residual_risk_rating' in job) {
      if (Array.isArray(job.risk_job_residual_risk_rating)) {
        job.risk_job_residual_risk_rating.forEach((rrr: any) => {
          if (rrr.rating) {
            const matrixItem = riskMatrix.find(
              item => item.value === rrr.rating,
            );
            if (matrixItem && typeof matrixItem.rate === 'number') {
              if (matrixItem.rate > maxRate) {
                maxRate = matrixItem.rate;
              }
            }
          }
        });
      }
    }
  });

  // Find the class for the maxRate
  if (maxRate > 0) {
    const maxRateObj = riskMatrix.find(item => item.rate === maxRate);
    if (maxRateObj) {
      return (
        maxRateObj.class.charAt(0).toUpperCase() +
        maxRateObj.class.slice(1).toLowerCase()
      );
    }
  }

  // Default fallback
  return 'Medium';
};

/**
 * Returns the background color for risk rating display components.
 *
 * @param {string} rating - The risk rating ('High', 'Medium', or 'Low')
 * @returns {string} Hex color code for the background
 *
 * @example
 * getRiskRatingBackgroundColor('High') // Returns: '#FAF2F5'
 * getRiskRatingBackgroundColor('Medium') // Returns: '#FFF8E1'
 * getRiskRatingBackgroundColor('Low') // Returns: '#F2FAF2'
 */
export const getRiskRatingBackgroundColor = (rating: string): string => {
  switch (rating) {
    case 'High':
      return '#FAF2F5';
    case 'Medium':
      return '#FFF8E1';
    default:
      // Low
      return '#F2FAF2';
  }
};

/**
 * Returns the text color for risk rating display components.
 *
 * @param {string} rating - The risk rating ('High', 'Medium', or 'Low')
 * @returns {string} Hex color code for the text
 *
 * @example
 * getRiskRatingTextColor('High') // Returns: '#C82333'
 * getRiskRatingTextColor('Medium') // Returns: '#FFA500'
 * getRiskRatingTextColor('Low') // Returns: '#218838'
 */
export const getRiskRatingTextColor = (rating: string): string => {
  switch (rating) {
    case 'High':
      return '#C82333';
    case 'Medium':
      return '#FFA500';
    default:
      // Low
      return '#218838';
  }
};

/**
 * Maps risk category data from API format to RiskCategory structure.
 *
 * @param {any} [input] - Category data from the API
 * @returns {RiskCategory} Formatted risk category object
 *
 * @description
 * Processes category data for risk assessments, handling both predefined
 * categories and custom 'other' category values.
 */
function mapRiskCategory(input?: any): RiskCategory {
  return {
    is_other: input?.is_other ?? false,
    category_id: _.compact(input?.map((item: any) => item?.category?.id)) ?? [],
    value: input?.value ?? '',
  };
}

/**
 * Maps job data from API format to RiskFormJob array structure.
 * Creates default job structure if no input provided.
 *
 * @param {any[]} [input] - Array of job data from the API
 * @returns {RiskFormJob[]} Array of formatted risk form job objects
 *
 * @description
 * Processes job data for risk assessments:
 * - Maps all job fields with fallback to empty strings
 * - Preserves risk rating arrays (initial and residual)
 * - Provides default empty job structure if no input
 */
function mapRiskJobs(input?: any[]): RiskFormJob[] {
  if (!Array.isArray(input) || !input.length) {
    return [
      {
        job_step: '',
        job_hazard: '',
        job_nature_of_risk: '',
        job_additional_mitigation: '',
        job_close_out_date: '',
        job_existing_control: '',
        job_close_out_responsibility_id: '',
        risk_job_initial_risk_rating: [],
        risk_job_residual_risk_rating: [],
      },
    ];
  }

  return input.map(job => ({
    job_step: job?.job_step ?? '',
    job_hazard: job?.job_hazard ?? '',
    job_nature_of_risk: job?.job_nature_of_risk ?? '',
    job_additional_mitigation: job?.job_additional_mitigation ?? '',
    job_close_out_date: job?.job_close_out_date ?? '',
    job_existing_control: job?.job_existing_control ?? '',
    job_close_out_responsibility_id: job?.job_close_out_responsibility_id ?? '',
    risk_job_initial_risk_rating: job?.risk_job_initial_risk_rating ?? [],
    risk_job_residual_risk_rating: job?.risk_job_residual_risk_rating ?? [],
  }));
}

/**
 * Maps task reliability assessment data from API format to RiskTaskReliabilityAssessment array.
 *
 * @param {any[]} [input] - Array of task reliability assessment data from the API
 * @returns {RiskTaskReliabilityAssessment[]} Array of formatted assessment objects
 *
 * @description
 * Processes reliability assessment data:
 * - Maps assessment ID, answer, and condition fields
 * - Provides fallback values for missing data
 * - Returns empty array if no input provided
 */
function mapRiskTaskReliabilityAssessment(
  input?: any[],
): RiskTaskReliabilityAssessment[] {
  if (!Array.isArray(input)) return [];
  return input.map(assessment => ({
    task_reliability_assessment_id:
      assessment?.task_reliability_assessment_id ?? 0,
    task_reliability_assessment_answer:
      assessment?.task_reliability_assessment_answer ?? '',
    condition: assessment?.condition ?? '',
  }));
}

/**
 * Creates a RiskForm object from API data or initializes with defaults.
 * Transforms raw API response into the structured format required by the risk assessment form.
 *
 * @param {any} [data] - Optional API data to transform into RiskForm
 * @returns {RiskForm} Complete RiskForm object with all required fields
 *
 * @description
 * Converts API response data into a properly structured RiskForm object:
 * - Maps basic fields (task details, assessor, vessel info, dates)
 * - Processes complex nested objects (categories, hazards, parameters, jobs)
 * - Handles approval workflow data (required approvals, approvers, RA level)
 * - Converts status codes to labels using raStatusToLabel mapping
 * - Calculates final approval date based on approver data and RA level
 * - Preserves metadata fields (created_by, updated_by, etc.)
 *
 * @example
 * const apiData = {
 *   task_requiring_ra: 'Engine Maintenance',
 *   vessel_ownership_id: 123,
 *   risk_job: [{ job_step: 'Preparation', ... }],
 *   status: 1 // DRAFT
 * };
 * const form = createRiskFormFromData(apiData);
 * // Returns complete RiskForm with all required fields populated
 */
export const createRiskFormFromData = (data?: any): RiskForm => {
  return {
    template_id: data?.template_id ?? undefined,
    task_requiring_ra: data?.task_requiring_ra ?? '',
    assessor: data?.assessor ?? undefined,
    vessel_ownership_id: data?.vessel_ownership_id ?? 0,
    office_id: data?.office_id ?? 0,
    office_name: data?.office_name ?? '',
    vessel_id: data?.vessel_id ?? undefined,
    date_risk_assessment: data?.date_risk_assessment ?? undefined,
    task_duration: data?.task_duration ?? '',
    task_alternative_consideration: data?.task_alternative_consideration ?? '',
    task_rejection_reason: data?.task_rejection_reason ?? '',
    worst_case_scenario: data?.worst_case_scenario ?? '',
    recovery_measures: data?.recovery_measures ?? '',
    status: data?.status ? raStatusToLabel[data?.status] : 'DRAFT',
    approval_required: data?.risk_approval_required?.length
      ? data?.risk_approval_required?.map(
          (item: any) => item?.approval_required?.id,
        )
      : [],
    risk_team_member:
      data?.risk_team_member?.map((member: any) => ({
        ...member,
        ...(member.email && {email: atob(member.email)}),
      })) ?? [],
    risk_category: mapRiskCategory(data?.risk_category),
    risk_hazard: mapHazards(data?.risk_hazards),
    parameters: mapParameters(data?.risk_parameter),
    risk_job: mapRiskJobs(data?.risk_job),
    risk_task_reliability_assessment: mapRiskTaskReliabilityAssessment(
      data?.risk_task_reliability_assessment,
    ),
    updated_at: data?.updated_at ?? undefined,
    updated_by: data?.updated_by ?? undefined,
    created_by: data?.created_by ?? undefined,
    ra_level: data?.ra_level ?? undefined,
    approval_date:
      getFinalApprovalDate(data?.risk_approver, data?.ra_level) ?? undefined,
    risk_approver: data?.risk_approver ?? [],
  };
};

/**
 * Groups risk parameters by parameter type for organized display.
 * Transforms flat parameter array into grouped structure for UI components.
 *
 * @param {RiskParameter[]} data - Array of risk parameters to group
 * @returns {Array} Array of grouped parameter objects with type information
 *
 * @description
 * Groups parameters by their parameter_type.id and creates structured objects:
 * - Groups by parameter type ID using lodash groupBy
 * - Extracts type information (id, name) from the first item in each group
 * - Maps parameter details (id, name) for each parameter in the group
 * - Returns empty array if input is not a valid array
 *
 * @example
 * const params = [
 *   { id: 1, name: 'PPE', parameter_type: { id: 1, name: 'Safety' } },
 *   { id: 2, name: 'Training', parameter_type: { id: 1, name: 'Safety' } }
 * ];
 * const grouped = groupRiskParameters(params);
 * // Returns: [{ id: 1, name: 'Safety', parameters: [{ id: 1, name: 'PPE' }, ...] }]
 */
export const groupRiskParameters = (data: RiskParameter[]) => {
  if (!Array.isArray(data)) return [];

  return Object.values(_.groupBy(data, item => item?.parameter_type?.id)).map(
    items => ({
      id: items[0]?.parameter_type?.id,
      name: items[0]?.parameter_type?.name,
      parameters: items.map(i => ({
        id: i?.id,
        name: i?.name,
      })),
    }),
  );
};

/**
 * Converts a template API response to a RiskForm object for creating risk assessments from templates.
 * Transforms template data structure into the format required by risk assessment forms.
 *
 * @param {any} payload - Template API response data
 * @returns {RiskForm} RiskForm object populated with template data
 *
 * @description
 * Transforms template data into risk assessment format:
 * - Maps basic template fields to corresponding risk form fields
 * - Converts template-specific objects (template_category, template_hazard, etc.) to risk equivalents
 * - Processes template jobs and converts risk ratings appropriately
 * - Handles parameter grouping and mapping from template format
 * - Initializes risk-specific fields (team members, approvals, vessel info) with defaults
 * - Preserves template metadata and assigns template_id reference
 *
 * Key transformations:
 * - template_category → risk_category
 * - template_hazard → risk_hazard
 * - template_job → risk_job (with rating structure conversion)
 * - template_parameter → parameters (with proper grouping)
 * - template_task_reliability_assessment → risk_task_reliability_assessment
 *
 * @example
 * const templateData = {
 *   id: 123,
 *   task_requiring_ra: 'Engine Maintenance',
 *   template_job: [{ job_step: 'Preparation', template_job_residual_risk_rating: [...] }]
 * };
 * const riskForm = transformTemplateToRisk(templateData);
 * // Returns RiskForm with template_id: 123 and all template data mapped appropriately
 */
export const transformTemplateToRisk = (payload: any): RiskForm => {
  // Map team members (if any, else empty array)
  const risk_team_member: RiskTeamMember[] = [];

  // Map risk category
  const risk_category: RiskCategory = {
    is_other: false,
    category_id: Array.isArray(payload?.template_category)
      ? payload.template_category.map((item: any) => item?.category?.id)
      : [],
    value: '', // No value in template_category, set as empty string
  };

  // Map risk hazard
  const risk_hazard: RiskHazard = {
    is_other: !!payload?.template_hazards?.find(
      (h: any) => h.hazard_category_is_other,
    ),
    hazard_id: Array.isArray(payload?.template_hazards)
      ? payload.template_hazards
          .filter((h: any) => !h.hazard_category_is_other && h.hazard_detail)
          .map((h: any) => h.hazard_detail?.id)
      : [],
    value:
      payload?.template_hazards?.find((h: any) => h.hazard_category_is_other)
        ?.value || '',
  };

  // Map parameters
  const parameters: Parameter[] = Array.isArray(payload?.template_parameter)
    ? (() => {
        // Group by parameterType.id
        const grouped: {[typeId: number]: any[]} = {};
        payload.template_parameter.forEach((item: any) => {
          const typeId = item?.parameterType?.id;
          if (!typeId) return;
          if (!grouped[typeId]) grouped[typeId] = [];
          grouped[typeId].push(item);
        });
        return Object.values(grouped).map((items: any[]) => {
          const is_other = !!items.find(i => i.parameter_is_other);
          const value = items.find(i => i.parameter_is_other)?.value ?? '';
          return {
            is_other,
            parameter_type_id: items[0]?.parameterType?.id,
            parameter_id: _.uniq(
              items.filter(i => i?.parameter?.id).map(i => i.parameter.id),
            ),
            value: is_other ? value : '',
          };
        });
      })()
    : [];

  // Map jobs
  const risk_job: RiskFormJob[] = Array.isArray(payload?.template_job)
    ? payload.template_job.map((job: any) => ({
        job_step: job.job_step ?? '',
        job_hazard: job.job_hazard ?? '',
        job_nature_of_risk: job.job_nature_of_risk ?? '',
        job_additional_mitigation: job.job_additional_mitigation ?? '',
        job_close_out_date: job.job_close_out_date ?? undefined,
        job_existing_control: job.job_existing_control ?? '',
        job_close_out_responsibility_id:
          job.job_close_out_responsibility_id ?? '',
        risk_job_initial_risk_rating: Array.isArray(
          job.template_job_initial_risk_rating,
        )
          ? job.template_job_initial_risk_rating.map(
              (r: any): RiskJobInitialRiskRating => ({
                parameter_type_id: r.parameter_type_id ?? 0,
                rating: r.rating,
              }),
            )
          : [],
        risk_job_residual_risk_rating: Array.isArray(
          job.template_job_residual_risk_rating,
        )
          ? job.template_job_residual_risk_rating.map(
              (
                r: TemplateFormJobResidualRiskRating,
              ): RiskJobResidualRiskRating => ({
                parameter_type_id: r.parameter_type_id ?? 0,
                rating: r.rating,
                reason: r.reason ?? '',
              }),
            )
          : [],
      }))
    : [];

  // Map task reliability assessment
  const risk_task_reliability_assessment: RiskTaskReliabilityAssessment[] =
    Array.isArray(payload?.template_task_reliability_assessment)
      ? payload.template_task_reliability_assessment.map((item: any) => ({
          task_reliability_assessment_id:
            item.task_reliability_assessment_id ?? item.id ?? 0,
          task_reliability_assessment_answer:
            item.task_reliability_assessment_answer ?? '',
          condition: item.condition ?? '',
        }))
      : [];

  // Map approval_required (not present in template, so set as empty array)
  // Map vessel_ownership_id (from RiskItem if present, else 0)
  // Map assessor (from RiskItem if present, else 0)
  // Map date_risk_assessment (from RiskItem if present, else '')

  return {
    task_requiring_ra: payload?.task_requiring_ra ?? '',
    assessor: payload?.assessor ?? null,
    vessel_ownership_id: payload?.vessel_ownership_id ?? null,
    office_id: payload?.office_id ?? null,
    office_name: payload?.office_name ?? '',
    date_risk_assessment: payload?.date_risk_assessment ?? null,
    task_duration: payload?.task_duration ?? '',
    task_alternative_consideration:
      payload?.task_alternative_consideration ?? '',
    task_rejection_reason: payload?.task_rejection_reason ?? '',
    worst_case_scenario: payload?.worst_case_scenario ?? '',
    recovery_measures: payload?.recovery_measures ?? '',
    status: TemplateFormStatus.DRAFT,
    approval_required: [],
    risk_team_member,
    risk_category,
    risk_hazard,
    parameters,
    risk_job,
    risk_task_reliability_assessment,
    updated_at: payload?.updated_at ?? undefined,
    updated_by: payload?.updated_by ?? undefined,
    created_by: payload?.created_by ?? undefined,
    ra_level: payload?.ra_level ?? undefined,
    approval_date: payload?.approval_date ?? undefined,
    risk_approver: payload?.risk_approver ?? [],
    template_id: payload?.id ?? undefined, // id of the template
  };
};

/**
 * Formats a Date object to YYYY-MM-DD string format without timezone issues.
 * Uses local date components to avoid timezone conversion problems.
 *
 * @param {Date} date - The Date object to format
 * @returns {string} Date string in YYYY-MM-DD format
 *
 * @description
 * Manually constructs date string using local date components:
 * - Extracts year, month, and day from the Date object
 * - Pads month and day with leading zeros if needed
 * - Avoids timezone conversion issues that can occur with toISOString()
 *
 * @example
 * const date = new Date(2023, 11, 25); // December 25, 2023
 * const formatted = formatDateToYYYYMMDD(date);
 * // Returns: '2023-12-25'
 */
export const formatDateToYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Calculates the final approval date for a risk assessment based on approvers and RA level.
 * Handles different approval workflows depending on the risk assessment level.
 *
 * @param {RAItemFull['risk_approver']} approvers - Array of risk approver objects
 * @param {RaLevel} raLevel - The Risk Assessment level (ROUTINE, CRITICAL, SPECIAL, etc.)
 * @returns {string | undefined} The final approval date or undefined if not fully approved
 *
 * @description
 * Approval logic varies by RA level:
 *
 * **ROUTINE Level:**
 * - Finds approvers without approval_order (direct approvers)
 * - Returns approval_date of the first approved user (status === 1)
 *
 * **CRITICAL/SPECIAL Levels:**
 * - Requires all reviewers with approval_order to be approved
 * - Checks that all have status === 1 and approval_status in [1, 3]
 * - Returns approval_date of the last approver in the sequence
 * - Returns undefined if not all reviewers have approved
 *
 * **Other Levels:**
 * - Returns undefined (no specific approval logic)
 *
 * @example
 * const approvers = [
 *   { approval_order: 1, status: 1, approval_status: 1, approval_date: '2023-12-01' },
 *   { approval_order: 2, status: 1, approval_status: 1, approval_date: '2023-12-02' }
 * ];
 * const finalDate = getFinalApprovalDate(approvers, RaLevel.CRITICAL);
 * // Returns: '2023-12-02' (last approver's date)
 */
export const getFinalApprovalDate = (
  approvers: RAItemFull['risk_approver'],
  raLevel: RaLevel,
) => {
  if (raLevel === RaLevel.ROUTINE) {
    return (
      approvers
        .filter(user => !user.approval_order)
        .find(user => user.status === 1)?.approval_date || undefined
    );
  }

  if ([RaLevel.CRITICAL, RaLevel.SPECIAL].includes(raLevel)) {
    const allReviewersApproved = approvers
      .filter(user => user.approval_order)
      .every(
        user => user.status === 1 && [1, 3].includes(user.approval_status ?? 0),
      );

    if (allReviewersApproved) {
      // Use slice().sort() to avoid mutating the original array
      const sortedApprovers = approvers
        .slice()
        .sort((a, b) => (a.approval_order ?? 0) - (b.approval_order ?? 0));
      return (
        sortedApprovers[sortedApprovers.length - 1]?.approval_date || undefined
      );
    }

    return undefined;
  }
};

/**
 * Interface for a single vessel/office option in dropdown components.
 * Represents an individual selectable item with vessel-specific metadata.
 */
export interface SingleVesselOfficeOption {
  value: number | string;
  label: string;
  vesselId?: number;
}

/**
 * Interface for grouped vessel/office options in dropdown components.
 * Represents a category of options (e.g., 'Active Vessels', 'Offices') with nested items.
 */
export interface GroupedVesselOfficeOption {
  label: string;
  data: Array<{
    id: number | string;
    name: string;
    vesselId?: number;
  }>;
}

/**
 * Creates grouped vessel/office options for dropdown components.
 * Organizes vessels by status and includes offices as a separate group.
 *
 * @param {any[]} vesselListForRisk - Array of vessel ownership data
 * @param {any[]} officeListForRisk - Array of office data
 * @param {Record<string, string>} vesselStatusAndLabelName - Mapping of vessel status codes to display labels
 * @returns {GroupedVesselOfficeOption[]} Array of grouped options for dropdown components
 *
 * @description
 * Processes vessel and office data into a structured format for grouped dropdowns:
 *
 * **Vessel Processing:**
 * - Groups vessels by their status (active, pending_handover, handed_over)
 * - Sorts groups alphabetically by status
 * - Uses vesselStatusAndLabelName to get display labels for each status
 * - Preserves vessel metadata (vesselId) for vessel-specific operations
 *
 * **Office Processing:**
 * - Creates a separate 'Offices' group
 * - Maps office data to consistent structure
 *
 * **Output Structure:**
 * - Each group has a label and data array
 * - Data items have id, name, and optional vesselId
 * - Maintains consistent interface for both vessels and offices
 *
 * @example
 * const vessels = [
 *   { id: 1, name: 'Ship A', status: 'active', vessel: { id: 101 } },
 *   { id: 2, name: 'Ship B', status: 'active', vessel: { id: 102 } }
 * ];
 * const offices = [{ id: 1, value: 'Singapore Office' }];
 * const statusLabels = { active: 'Active Vessels' };
 *
 * const grouped = createGroupedVesselOfficeOptions(vessels, offices, statusLabels);
 * // Returns: [
 * //   { label: 'Active Vessels', data: [{ id: 1, name: 'Ship A', vesselId: 101 }, ...] },
 * //   { label: 'Offices', data: [{ id: 1, name: 'Singapore Office' }] }
 * // ]
 */
export const createGroupedVesselOfficeOptions = (
  vesselListForRisk: any[],
  officeListForRisk: any[],
  vesselStatusAndLabelName: Record<string, string>,
): GroupedVesselOfficeOption[] => {
  const vesselOpts = vesselListForRisk.map(item => ({
    value: item.id,
    label: item.name,
    vesselId: item.vessel.id,
    status: item.status,
  }));

  const officeOpts = officeListForRisk.map(item => ({
    value: item.id,
    label: item.value,
  }));

  // Create grouped options for the dropdown
  const vesselsByStatus = _.groupBy(vesselOpts, 'status');
  const groupedOptions: GroupedVesselOfficeOption[] = [
    ...Object.entries(vesselsByStatus)
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([status, vessels]) => ({
        label: vesselStatusAndLabelName[String(status)] || status,
        data: vessels.map(vessel => ({
          id: vessel.value,
          name: vessel.label,
          vesselId: vessel.vesselId,
        })),
      })),
    {
      label: 'Offices',
      data: officeOpts.map(office => ({
        id: office.value,
        name: office.label,
      })),
    },
  ];

  return groupedOptions;
};

/**
 * Finds a selected vessel/office option from grouped options by its value.
 * Searches through all groups to locate the option with the matching ID.
 *
 * @param {GroupedVesselOfficeOption[]} groupedOptions - Array of grouped options to search through
 * @param {number | string | null} currentValue - The value/ID to search for
 * @returns {SingleVesselOfficeOption | null} The matching option or null if not found
 *
 * @description
 * Searches through the grouped options structure to find a specific item:
 * - Iterates through all groups (vessel status groups and offices)
 * - Searches within each group's data array for matching ID
 * - Returns the first match found with proper structure
 * - Preserves vesselId metadata if present
 * - Returns null if no match found or currentValue is null/undefined
 *
 * This is useful for:
 * - Setting initial selected values in dropdowns
 * - Validating selected options
 * - Converting between ID and full option objects
 *
 * @example
 * const grouped = [
 *   { label: 'Active Vessels', data: [{ id: 1, name: 'Ship A', vesselId: 101 }] },
 *   { label: 'Offices', data: [{ id: 2, name: 'Office B' }] }
 * ];
 *
 * const option = findSelectedVesselOfficeOption(grouped, 1);
 * // Returns: { value: 1, label: 'Ship A', vesselId: 101 }
 *
 * const notFound = findSelectedVesselOfficeOption(grouped, 999);
 * // Returns: null
 */
export const findSelectedVesselOfficeOption = (
  groupedOptions: GroupedVesselOfficeOption[],
  currentValue: number | string | null,
): SingleVesselOfficeOption | null => {
  if (!currentValue) return null;

  for (const group of groupedOptions) {
    const found = group.data.find(item => item.id === currentValue);
    if (found) {
      return {
        value: found.id,
        label: found.name,
        vesselId: (found as any).vesselId,
      };
    }
  }
  return null;
};
