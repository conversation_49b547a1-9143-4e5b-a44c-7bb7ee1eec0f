.user-multiselect-root {
  position: relative;
  width: 100%;
}

.user-multiselect-input {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  width: 100%;
  box-sizing: border-box;
  gap: 0;

  .user-multiselect-names {
    flex: 1 1 0%;
    min-width: 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;

    .user-multiselect-placeholder {
      color: #757575;
      font-weight: 400;
    }

    .user-multiselect-names-list {
      flex: 1 1 0%;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .user-multiselect-counter {
    margin-left: 4px;
    flex-shrink: 0;
    display: inline-block;
  }

  .user-multiselect-chevron {
    flex-shrink: 0;
    margin-left: 8px;
    display: flex;
    align-items: center;
  }
}

.user-selector-dropdown {
  position: fixed;
  z-index: 9999;
  background: #fff;
  border: 1px solid #dbe2ea;
  border-radius: 9px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.07);
  max-width: 389px;
  /* Position will be set dynamically */
}

.user-selector-container {
  background: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  overflow: hidden;

  .search-bar-section {
    padding: 8px;
  }

  .user-list-section {
    max-height: 306px;
    overflow-y: auto;
    padding: 0 8px;
    margin-bottom: 8px;
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 6px;
    }
  }
  .user-list-item {
    display: flex !important;
    align-items: center !important;
    padding: 8px !important;
    cursor: pointer !important;
    transition: background 0.1s !important;
    &:hover {
      background: #f8fafc !important;
    }
    .avatar {
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin: 0px 12px;

      width: 48px;
      height: 48px;

      background: #e5f4f8;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      text-align: center;
      color: #1f4a70;
    }
    .user-info {
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .user-name {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;

        color: #333333;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .user-details {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;

        color: #6c757d;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .empty-list-message {
    padding: 1rem;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #6c757d;
  }

  .footer-section {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 8px 12px 16px;
    gap: 10px;

    margin: 0 auto;
    height: 36px;

    border-top: 1px solid #cccccc;

    button {
      all: unset;
      color: #1f4a70;
      text-decoration-color: #1f4a70;
      cursor: pointer;

      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      text-decoration-line: underline;
      border: 0px;
    }
  }
}
