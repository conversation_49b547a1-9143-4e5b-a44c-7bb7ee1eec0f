import React from 'react';
import {render} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import AppRoutes from '../src/routes/rootRoutes';
import mockedRoutesConfig from '../src/routes/route.config';
import {useDataStoreContext} from '../src/context';

// Mock useDataStoreContext
jest.mock('../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

// Mock routesConfig and IRoute
jest.mock('../src/routes/route.config', () => {
  return {
    __esModule: true,
    default: jest.fn(),
    IRoute: {},
  };
});

describe('AppRoutes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    (mockedRoutesConfig as jest.Mock).mockReturnValue([]);
    (useDataStoreContext as jest.Mock).mockReturnValue({roleConfig: {}});

    expect(() => {
      render(
        <MemoryRouter initialEntries={['/']}>
          <AppRoutes />
        </MemoryRouter>,
      );
    }).not.toThrow();
  });
});
