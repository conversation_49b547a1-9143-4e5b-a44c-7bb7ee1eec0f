import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TemplateListingFilters } from '../../../../src/pages/RATemplateListing/components/TemplateListingFilters';

// Mocks
jest.mock('react-bootstrap', () => ({
  Row: (props: any) => <div data-testid="mock-row">{props.children}</div>,
  Col: (props: any) => <div data-testid="mock-col">{props.children}</div>,
  Form: { Group: (props: any) => <div data-testid="mock-form-group">{props.children}</div> },
}));
jest.mock('react-toastify', () => ({ toast: { error: jest.fn() } }));
jest.mock('../../../../src/components/SearchInput', () => (props: any) => (
  <input data-testid="mock-search-input" value={props.value} onChange={e => props.onSearch(e.target.value)} placeholder={props.placeholder} />
));
jest.mock('../../../../src/components/DropdownTypeahead', () => (props: any) => (
  <select data-testid="mock-dropdown-typeahead" onChange={e => props.onChange(e.target.value)} multiple>
    <option value="1">Option1</option>
    <option value="2">Option2</option>
  </select>
));
jest.mock('../../../../src/components/CategoriesFiltersDrawer', () => ({
  CategoriesFiltersDrawer: (props: any) => (
    <div data-testid="mock-categories-filters-drawer">CategoriesFiltersDrawer</div>
  ),
}));
jest.mock('../../../../src/components/CustomDatePickerWithRange', () => (props: any) => (
  <input data-testid="mock-date-picker" onChange={e => props.onChange([e.target.value, e.target.value])} />
));
jest.mock('../../../../src/components/SearchUserDropdown', () => (props: any) => (
  <input data-testid="mock-user-dropdown" value={props.value} onChange={e => props.onChange([e.target.value])} />
));

const mockSetDataStore = jest.fn();
jest.mock('../../../../src/context', () => ({
  useDataStoreContext: () => ({ setDataStore: mockSetDataStore })
}));

jest.mock('../../../../src/services/services', () => ({
  getHazardsList: jest.fn(() => Promise.resolve([])),
  getRiskCategoryList: jest.fn(() => Promise.resolve([])),
  getTemplateUserList: jest.fn(() => Promise.resolve([{ userId: 1, email: '<EMAIL>', full_name: 'User One', designation: 'Desig' }])),
}));

describe('TemplateListingFilters', () => {
  const filters = {
    search: '',
    created_by: null,
    created_at: null,
    ra_categories: null,
    hazard_categories: null,
    template_category: null,
  };
  const onFilterChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all filter controls', async () => {
    render(<TemplateListingFilters filters={filters} onFilterChange={onFilterChange} />);
    expect(screen.getAllByTestId('mock-form-group').length).toBeGreaterThan(0);
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    expect(screen.getByTestId('mock-user-dropdown')).toBeInTheDocument();
    expect(screen.getByTestId('mock-date-picker')).toBeInTheDocument();
    expect(screen.getByTestId('mock-dropdown-typeahead')).toBeInTheDocument();
    expect(screen.getByTestId('mock-categories-filters-drawer')).toBeInTheDocument();
    // Wait for useEffect to finish
    await waitFor(() => expect(mockSetDataStore).toHaveBeenCalled());
  });

  it('calls onFilterChange for search input', () => {
    render(<TemplateListingFilters filters={filters} onFilterChange={onFilterChange} />);
    fireEvent.change(screen.getByTestId('mock-search-input'), { target: { value: 'abc' } });
    expect(onFilterChange).toHaveBeenCalledWith('search', 'abc');
  });

  it('calls onFilterChange for user dropdown', () => {
    render(<TemplateListingFilters filters={filters} onFilterChange={onFilterChange} />);
    fireEvent.change(screen.getByTestId('mock-user-dropdown'), { target: { value: 'user1' } });
    expect(onFilterChange).toHaveBeenCalledWith('created_by', ['user1']);
  });

  it('calls onFilterChange for date picker', () => {
    render(<TemplateListingFilters filters={filters} onFilterChange={onFilterChange} />);
    fireEvent.change(screen.getByTestId('mock-date-picker'), { target: { value: '2024-01-01' } });
    expect(onFilterChange).toHaveBeenCalledWith('created_at', ['2024-01-01', '2024-01-01']);
  });

  it('calls onFilterChange for dropdown typeahead', () => {
    render(<TemplateListingFilters filters={filters} onFilterChange={onFilterChange} />);
    fireEvent.change(screen.getByTestId('mock-dropdown-typeahead'), { target: { value: '1' } });
    expect(onFilterChange).toHaveBeenCalled();
  });

  it('handles error in useEffect', async () => {
    const { getHazardsList } = require('../../../../src/services/services');
    getHazardsList.mockImplementationOnce(() => Promise.reject('fail'));
    render(<TemplateListingFilters filters={filters} onFilterChange={onFilterChange} />);
    await waitFor(() => {
      const { toast } = require('react-toastify');
      expect(toast.error).toHaveBeenCalled();
    });
  });
});
