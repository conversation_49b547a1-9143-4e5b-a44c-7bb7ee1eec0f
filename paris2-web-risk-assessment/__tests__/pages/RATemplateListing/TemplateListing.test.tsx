import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import TemplateListing, { getColumns } from '../../../src/pages/RATemplateListing/TemplateListing';
import { BasicUserDetails } from '../../../src/types/user';
import { TemplateListResponse } from '../../../src/types/template';

// Mocks for all child components and hooks
jest.mock('../../../src/components/InfiniteScrollTable', () => (props: any) => (
  <div data-testid="mock-infinite-scroll-table">
    <button
      data-testid="trigger-sorting-change"
      onClick={() => props.sorting?.onSortingChange?.([])}
    >
      Clear Sorting
    </button>
    <button
      data-testid="trigger-sorting-change-with-value"
      onClick={() => props.sorting?.onSortingChange?.([{id: 'task_requiring_ra', desc: false}])}
    >
      Set Sorting
    </button>
  </div>
));
jest.mock('../../../src/pages/RATemplateListing/components/TemplateListingHeader', () => ({
  TemplateListingHeader: (props: any) => <div data-testid="mock-header">Header</div>,
}));
jest.mock('../../../src/pages/RATemplateListing/components/TemplateListingFilters', () => ({
  TemplateListingFilters: (props: any) => (
    <div data-testid="mock-filters">
      <button
        data-testid="trigger-filter-change"
        onClick={() => props.onFilterChange?.('search', 'test search')}
      >
        Change Filter
      </button>
    </div>
  ),
}));
jest.mock('../../../src/pages/RATemplateListing/components/MostlyUsedCard', () => ({
  MostlyUsedCardList: () => <div data-testid="mock-mostly-used">MostlyUsedCardList</div>,
}));
jest.mock('../../../src/pages/RATemplateListing/components/ActionDropdownMenu', () => ({
  ActionDropdownMenu: () => <div data-testid="mock-action-dropdown">ActionDropdownMenu</div>,
}));
jest.mock('../../../src/components/SingleBadgePopover', () => (props: any) => (
  <div data-testid="mock-single-badge-popover">{props.label}</div>
));
jest.mock('../../../src/components/BadgeListPopover', () => (props: any) => (
  <div data-testid="mock-badge-list">BadgeList</div>
));
jest.mock('../../../src/components/TruncateBasicText', () => (props: any) => (
  <span data-testid="mock-truncate-text">{props.text}</span>
));
jest.mock('../../../src/context', () => ({
  useDataStoreContext: () => ({
    roleConfig: {
      riskAssessment: {
        canViewTemplate: true,
        canArchiveTemplate: true
      }
    }
  }),
}));

jest.mock('../../../src/hooks', () => ({
  useInfiniteQuery: jest.fn(() => ({
    data: { data: [], pagination: { page: 1, pageSize: 10, totalItems: 0, totalPages: 1 }, userDetails: [] },
    isFetchingNextPage: false,
    isLoading: false,
    fetchNextPage: jest.fn(),
    reset: jest.fn(),
  })),
}));

jest.mock('../../../src/services/services', () => ({
  getTemplateList: jest.fn(),
}));

describe('TemplateListing', () => {
  it('renders header, filters, mostly used card, and table', () => {
    render(<TemplateListing />);
    expect(screen.getByTestId('mock-header')).toBeInTheDocument();
    expect(screen.getByTestId('mock-filters')).toBeInTheDocument();
    expect(screen.getByTestId('mock-mostly-used')).toBeInTheDocument();
    expect(screen.getByText('All Templates')).toBeInTheDocument();
    expect(screen.getByTestId('mock-infinite-scroll-table')).toBeInTheDocument();
  });

  it('renders table with correct sorting callback', () => {
    render(<TemplateListing />);
    // Sorting is passed as a prop, but we just check the table is rendered
    expect(screen.getByTestId('mock-infinite-scroll-table')).toBeInTheDocument();
  });
});

describe('getColumns', () => {
  const reset = jest.fn();
  const rowData: any = {
    task_requiring_ra: 'Task',
    template_category: [
      { category_is_other: false, category: { name: 'Cat1' }, value: null },
      { category_is_other: true, value: 'OtherCat' },
    ],
    template_hazards: [
      { hazard_category_is_other: false, hazard_detail: { name: 'Haz1' }, value: null },
      { hazard_category_is_other: true, value: 'OtherHaz' },
    ],
    created_at: '2024-01-01',
    created_by: '1',
    template_keywords: [ { name: 'kw1' }, { name: 'kw2' } ],
    id: 123,
  };
  const columns = getColumns([{ userId: '1', email: '<EMAIL>', full_name: 'User One', designation: '' }], reset);

  // NOTE: We use 'as any' for the context object to satisfy TanStack Table's cell renderer signature in tests.
  it('renders Task Required cell', () => {
    const cellFn = columns[0].cell;
    if (typeof cellFn === 'function') {
      const cell = cellFn({ getValue: () => 'Task' } as any);
      expect(cell.props.text).toBe('Task');
    }
  });

  it('renders No. of Risk Categories cell with values', () => {
    const cellFn = columns[1].cell;
    if (typeof cellFn === 'function') {
      const cell = cellFn({ getValue: () => rowData.template_category } as any);
      expect(cell.props.items).toContain('Cat1');
      expect(cell.props.items).toContain('OtherCat');
    }
  });

  it('renders No. of Risk Categories cell with empty array', () => {
    const cellFn = columns[1].cell;
    if (typeof cellFn === 'function') {
      const cell = cellFn({ getValue: () => [] } as any);
      expect(cell).not.toBeNull();
    }
  });

  it('renders No. of Hazard Categories cell with values', () => {
    const cellFn = columns[2].cell;
    if (typeof cellFn === 'function') {
      const cell = cellFn({ getValue: () => rowData.template_hazards } as any);
      expect(cell.props.items).toContain('Haz1');
      expect(cell.props.items).toContain('OtherHaz');
    }
  });

  it('renders No. of Hazard Categories cell with empty array', () => {
    const cellFn = columns[2].cell;
    if (typeof cellFn === 'function') {
      const cell = cellFn({ getValue: () => [] } as any);
      expect(cell).not.toBeNull();
    }
  });

  it('renders Created on cell', () => {
    const cellFn = columns[3].cell;
    if (typeof cellFn === 'function') {
      const cell = cellFn({ getValue: () => '2024-01-01' } as any);
      expect(cell).toBeDefined();
    }
  });

  it('renders Created by cell with user', () => {
    const cellFn = columns[4].cell;
    if (typeof cellFn === 'function') {
      const cell = cellFn({ getValue: () => '1' } as any);
      // Should render a span with a TruncateText child
      const child = Array.isArray(cell.props.children)
        ? cell.props.children[0]
        : cell.props.children;
      expect(child.props.text).toBe('<EMAIL>');
    }
  });

  it('renders Created by cell with missing user', () => {
    const cellFn = columns[4].cell;
    if (typeof cellFn === 'function') {
      const cell = cellFn({ getValue: () => 'notfound' } as any);
      const child = Array.isArray(cell.props.children)
        ? cell.props.children[1]
        : cell.props.children;
      expect(child.props.className).toBe('text-muted');
    }
  });

  it('renders Keywords cell with values', () => {
    const cellFn = columns[5].cell;
    if (typeof cellFn === 'function') {
      const cell = cellFn({ getValue: () => rowData.template_keywords } as any);
      // Render the cell and assert on the BadgeListPopover mock
      render(cell);
      expect(screen.getByTestId('mock-badge-list')).toBeInTheDocument();
    }
  });

  it('renders Keywords cell with empty array', () => {
    const cellFn = columns[5].cell;
    if (typeof cellFn === 'function') {
      const cell = cellFn({ getValue: () => [] } as any);
      expect(cell).not.toBeNull();
    }
  });

  it('renders Action cell', () => {
    const cellFn = columns[6].cell;
    if (typeof cellFn === 'function') {
      const row = { original: { ...rowData, created_by: '1' } };
      const cell = cellFn({ row } as any);
      render(cell);
      expect(screen.getByTestId('mock-action-dropdown')).toBeInTheDocument();
    }
  });

  it('renders Action cell with missing user', () => {
    const cellFn = columns[6].cell;
    if (typeof cellFn === 'function') {
      const row = { original: { ...rowData, created_by: 'notfound' } };
      const cell = cellFn({ row } as any);
      render(cell);
      expect(screen.getByTestId('mock-action-dropdown')).toBeInTheDocument();
    }
  });
});

describe('TemplateListing integration', () => {
  it('calls sorting callback and resets sorting when empty array is passed', () => {
    render(<TemplateListing />);

    const clearSortingButton = screen.getByTestId('trigger-sorting-change');
    fireEvent.click(clearSortingButton);

    // The component should handle empty sorting array
    expect(screen.getByTestId('mock-infinite-scroll-table')).toBeInTheDocument();
  });

  it('calls sorting callback with new sorting value', () => {
    render(<TemplateListing />);

    const setSortingButton = screen.getByTestId('trigger-sorting-change-with-value');
    fireEvent.click(setSortingButton);

    // The component should handle new sorting value
    expect(screen.getByTestId('mock-infinite-scroll-table')).toBeInTheDocument();
  });

  it('calls handleFilterChange when filter is changed', () => {
    render(<TemplateListing />);

    const filterChangeButton = screen.getByTestId('trigger-filter-change');
    fireEvent.click(filterChangeButton);

    // The component should handle filter changes
    expect(screen.getByTestId('mock-filters')).toBeInTheDocument();
  });
});
