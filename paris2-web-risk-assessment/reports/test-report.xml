<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/ExitPageModal.test.tsx">
        <testCase name="ExitPageModal Component Rendering renders modal with correct title" duration="42"/>
        <testCase name="ExitPageModal Component Rendering renders modal body with warning message" duration="5"/>
        <testCase name="ExitPageModal Component Rendering renders both action buttons" duration="6"/>
        <testCase name="ExitPageModal Component Rendering applies correct CSS classes to modal" duration="3"/>
        <testCase name="ExitPageModal Component Rendering sets correct role attribute on alert" duration="4"/>
        <testCase name="ExitPageModal Modal Properties renders modal as shown by default" duration="3"/>
        <testCase name="ExitPageModal Modal Properties has correct modal size" duration="3"/>
        <testCase name="ExitPageModal Modal Properties has static backdrop" duration="3"/>
        <testCase name="ExitPageModal Modal Properties has correct dialog class name" duration="2"/>
        <testCase name="ExitPageModal Button Interactions calls onConfirm when Exit Page button is clicked" duration="4"/>
        <testCase name="ExitPageModal Button Interactions calls onClose when Assign Approvers button is clicked" duration="3"/>
        <testCase name="ExitPageModal Button Interactions calls onClose when modal is hidden via onHide" duration="4"/>
        <testCase name="ExitPageModal Button Styling applies correct CSS classes to Exit Page button" duration="4"/>
        <testCase name="ExitPageModal Button Styling applies correct CSS classes to Assign Approvers button" duration="2"/>
        <testCase name="ExitPageModal Button Styling has correct button variants" duration="2"/>
        <testCase name="ExitPageModal Modal Body Styling applies correct class to modal body" duration="2"/>
        <testCase name="ExitPageModal Accessibility has proper alert role for warning message" duration="2"/>
        <testCase name="ExitPageModal Accessibility has strong emphasis on warning text" duration="2"/>
        <testCase name="ExitPageModal Accessibility buttons are focusable and clickable" duration="2"/>
        <testCase name="ExitPageModal Component Structure renders modal header with title" duration="2"/>
        <testCase name="ExitPageModal Component Structure renders modal body with alert" duration="2"/>
        <testCase name="ExitPageModal Component Structure renders modal footer with buttons" duration="3"/>
        <testCase name="ExitPageModal Props Handling handles onClose prop correctly" duration="3"/>
        <testCase name="ExitPageModal Props Handling handles onConfirm prop correctly" duration="4"/>
        <testCase name="ExitPageModal Props Handling calls onClose function directly without parameters" duration="3"/>
        <testCase name="ExitPageModal Multiple Interactions handles multiple button clicks correctly" duration="3"/>
    </file>
</testExecutions>